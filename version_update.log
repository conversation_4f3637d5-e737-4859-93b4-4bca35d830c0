[0;34m[2025-08-21 19:14:06][0m === Apache Spark 版本更新开始 ===
[0;34m[2025-08-21 19:14:06][0m 当前目录: /Users/<USER>/Documents/IdeaProjects-git/bag_spark_branch-3.4
[0;34m[2025-08-21 19:14:06][0m 目标: 3.4.4 -> 3.4.4_20250821
[0;34m[2025-08-21 19:14:12][0m 检查必要工具...
[0;32m[SUCCESS][0m 工具检查完成
[0;34m[2025-08-21 19:14:12][0m 创建备份目录: version_backup_20250821_191406
[0;34m[2025-08-21 19:14:12][0m 备份: ./resource-managers/yarn/pom.xml -> version_backup_20250821_191406./resource-managers/yarn/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./resource-managers/mesos/pom.xml -> version_backup_20250821_191406./resource-managers/mesos/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./resource-managers/kubernetes/core/pom.xml -> version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./resource-managers/kubernetes/integration-tests/pom.xml -> version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./launcher/pom.xml -> version_backup_20250821_191406./launcher/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./tools/pom.xml -> version_backup_20250821_191406./tools/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./core/pom.xml -> version_backup_20250821_191406./core/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./hadoop-cloud/pom.xml -> version_backup_20250821_191406./hadoop-cloud/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./assembly/pom.xml -> version_backup_20250821_191406./assembly/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./graphx/pom.xml -> version_backup_20250821_191406./graphx/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./mllib/pom.xml -> version_backup_20250821_191406./mllib/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./repl/pom.xml -> version_backup_20250821_191406./repl/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./pom.xml -> version_backup_20250821_191406./pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./streaming/pom.xml -> version_backup_20250821_191406./streaming/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./mllib-local/pom.xml -> version_backup_20250821_191406./mllib-local/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./common/network-yarn/pom.xml -> version_backup_20250821_191406./common/network-yarn/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./common/network-common/pom.xml -> version_backup_20250821_191406./common/network-common/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./common/network-shuffle/pom.xml -> version_backup_20250821_191406./common/network-shuffle/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./common/tags/pom.xml -> version_backup_20250821_191406./common/tags/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./common/unsafe/pom.xml -> version_backup_20250821_191406./common/unsafe/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./common/kvstore/pom.xml -> version_backup_20250821_191406./common/kvstore/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./common/sketch/pom.xml -> version_backup_20250821_191406./common/sketch/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./examples/pom.xml -> version_backup_20250821_191406./examples/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./connector/kafka-0-10-assembly/pom.xml -> version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
[0;34m[2025-08-21 19:14:12][0m 备份: ./connector/kinesis-asl-assembly/pom.xml -> version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/kafka-0-10-sql/pom.xml -> version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/kafka-0-10/pom.xml -> version_backup_20250821_191406./connector/kafka-0-10/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/kinesis-asl/pom.xml -> version_backup_20250821_191406./connector/kinesis-asl/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/docker-integration-tests/pom.xml -> version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/spark-ganglia-lgpl/pom.xml -> version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/avro/pom.xml -> version_backup_20250821_191406./connector/avro/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/kafka-0-10-token-provider/pom.xml -> version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/connect/server/pom.xml -> version_backup_20250821_191406./connector/connect/server/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/connect/common/pom.xml -> version_backup_20250821_191406./connector/connect/common/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/connect/client/jvm/pom.xml -> version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./connector/protobuf/pom.xml -> version_backup_20250821_191406./connector/protobuf/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./sql/core/pom.xml -> version_backup_20250821_191406./sql/core/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./sql/catalyst/pom.xml -> version_backup_20250821_191406./sql/catalyst/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./sql/hive/pom.xml -> version_backup_20250821_191406./sql/hive/pom.xml
[0;34m[2025-08-21 19:14:13][0m 备份: ./sql/hive-thriftserver/pom.xml -> version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
[0;32m[SUCCESS][0m 备份完成
[0;34m[2025-08-21 19:14:13][0m 更新主pom.xml版本...
[0;32m[SUCCESS][0m 主pom.xml版本更新成功
[0;34m[2025-08-21 19:14:13][0m 更新子模块pom.xml中的父版本引用...
[0;34m[2025-08-21 19:14:13][0m 处理: ./resource-managers/yarn/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/yarn/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./resource-managers/mesos/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/mesos/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./resource-managers/kubernetes/core/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/kubernetes/core/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./resource-managers/kubernetes/integration-tests/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/kubernetes/integration-tests/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./launcher/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./launcher/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./tools/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./tools/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./core/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./core/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./hadoop-cloud/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./hadoop-cloud/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./assembly/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./assembly/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./graphx/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./graphx/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./mllib/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./mllib/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./repl/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./repl/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./streaming/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./streaming/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./mllib-local/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./mllib-local/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./common/network-yarn/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./common/network-yarn/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./common/network-common/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./common/network-common/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./common/network-shuffle/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./common/network-shuffle/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./common/tags/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./common/tags/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./common/unsafe/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./common/unsafe/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./common/kvstore/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./common/kvstore/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./common/sketch/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./common/sketch/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./examples/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./examples/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./version_backup_20250821_191406./resource-managers/yarn/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./resource-managers/yarn/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./version_backup_20250821_191406./resource-managers/mesos/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./resource-managers/mesos/pom.xml
[0;34m[2025-08-21 19:14:13][0m 处理: ./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./launcher/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./launcher/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./tools/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./tools/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./core/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./core/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./hadoop-cloud/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./hadoop-cloud/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./assembly/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./assembly/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./graphx/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./graphx/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./mllib/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./mllib/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./repl/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./repl/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./pom.xml
[1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./streaming/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./streaming/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./mllib-local/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./mllib-local/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./common/network-yarn/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./common/network-yarn/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./common/network-common/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./common/network-common/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./common/network-shuffle/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./common/network-shuffle/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./common/tags/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./common/tags/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./common/unsafe/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./common/unsafe/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./common/kvstore/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./common/kvstore/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./common/sketch/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./common/sketch/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./examples/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./examples/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/avro/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/avro/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/connect/server/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/connect/server/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/connect/common/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/connect/common/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./connector/protobuf/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./connector/protobuf/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./sql/core/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./sql/core/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./sql/catalyst/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./sql/catalyst/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./sql/hive/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./sql/hive/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/kafka-0-10-assembly/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10-assembly/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/kinesis-asl-assembly/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/kinesis-asl-assembly/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/kafka-0-10-sql/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10-sql/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/kafka-0-10/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/kinesis-asl/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/kinesis-asl/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/docker-integration-tests/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/docker-integration-tests/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/spark-ganglia-lgpl/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/spark-ganglia-lgpl/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/avro/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/avro/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/kafka-0-10-token-provider/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10-token-provider/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/connect/server/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/connect/server/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/connect/common/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/connect/common/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/connect/client/jvm/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/connect/client/jvm/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./connector/protobuf/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./connector/protobuf/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./sql/core/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./sql/core/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./sql/catalyst/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./sql/catalyst/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./sql/hive/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./sql/hive/pom.xml
[0;34m[2025-08-21 19:14:14][0m 处理: ./sql/hive-thriftserver/pom.xml
[0;32m[SUCCESS][0m   父版本更新成功: ./sql/hive-thriftserver/pom.xml
[0;32m[SUCCESS][0m 子模块父版本更新完成
[0;34m[2025-08-21 19:14:14][0m 验证版本更新...
[0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./pom.xml
[0;32m[SUCCESS][0m 版本验证通过
[0;34m[2025-08-21 19:14:15][0m 清理临时文件...
[0;32m[SUCCESS][0m 清理完成
[0;34m[2025-08-21 19:14:16][0m === 版本更新摘要 ===
[0;34m[2025-08-21 19:14:16][0m 原版本: 3.4.4
[0;34m[2025-08-21 19:14:16][0m 新版本: 3.4.4_20250821
[0;34m[2025-08-21 19:14:16][0m 备份目录: version_backup_20250821_191406
[0;34m[2025-08-21 19:14:16][0m 日志文件: version_update.log
[0;34m[2025-08-21 19:14:16][0m 更新的pom.xml文件数量:       80
[0;34m[2025-08-21 19:14:16][0m === 更新完成 ===
[0;32m[SUCCESS][0m 版本更新成功完成！
-e [0;34m[2025-08-21 20:48:15][0m === Apache Spark 版本更新开始 ===
-e [0;34m[2025-08-21 20:48:15][0m 当前目录: /Users/<USER>/Documents/IdeaProjects-git/bag_spark_branch-3.4
-e [0;34m[2025-08-21 20:48:15][0m 目标: 3.4.4 -> 3.4.4_SNAPSHOT
-e [0;34m[2025-08-21 20:48:18][0m 检查必要工具...
-e [0;32m[SUCCESS][0m 工具检查完成
-e [0;34m[2025-08-21 20:48:18][0m 创建备份目录: version_backup_20250821_204815
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./resource-managers/yarn/pom.xml -> version_backup_20250821_204815./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./resource-managers/mesos/pom.xml -> version_backup_20250821_204815./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./resource-managers/kubernetes/core/pom.xml -> version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./resource-managers/kubernetes/integration-tests/pom.xml -> version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./launcher/pom.xml -> version_backup_20250821_204815./launcher/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./tools/pom.xml -> version_backup_20250821_204815./tools/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./core/pom.xml -> version_backup_20250821_204815./core/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./hadoop-cloud/pom.xml -> version_backup_20250821_204815./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./assembly/pom.xml -> version_backup_20250821_204815./assembly/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./graphx/pom.xml -> version_backup_20250821_204815./graphx/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./mllib/pom.xml -> version_backup_20250821_204815./mllib/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./repl/pom.xml -> version_backup_20250821_204815./repl/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./pom.xml -> version_backup_20250821_204815./pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./streaming/pom.xml -> version_backup_20250821_204815./streaming/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./mllib-local/pom.xml -> version_backup_20250821_204815./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./common/network-yarn/pom.xml -> version_backup_20250821_204815./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./common/network-common/pom.xml -> version_backup_20250821_204815./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./common/network-shuffle/pom.xml -> version_backup_20250821_204815./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./common/tags/pom.xml -> version_backup_20250821_204815./common/tags/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./common/unsafe/pom.xml -> version_backup_20250821_204815./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./common/kvstore/pom.xml -> version_backup_20250821_204815./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./common/sketch/pom.xml -> version_backup_20250821_204815./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./examples/pom.xml -> version_backup_20250821_204815./examples/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./resource-managers/yarn/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./resource-managers/mesos/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./launcher/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./tools/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./core/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./hadoop-cloud/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./assembly/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./graphx/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./mllib/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./repl/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./streaming/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./mllib-local/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./common/network-yarn/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./common/network-common/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./common/network-shuffle/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./common/tags/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./common/unsafe/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./common/kvstore/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./common/sketch/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./examples/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/kafka-0-10/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/kinesis-asl/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/avro/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/connect/server/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/connect/common/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./connector/protobuf/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./sql/core/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./sql/catalyst/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./sql/hive/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml -> version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/kafka-0-10-assembly/pom.xml -> version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/kinesis-asl-assembly/pom.xml -> version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/kafka-0-10-sql/pom.xml -> version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/kafka-0-10/pom.xml -> version_backup_20250821_204815./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/kinesis-asl/pom.xml -> version_backup_20250821_204815./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/docker-integration-tests/pom.xml -> version_backup_20250821_204815./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/spark-ganglia-lgpl/pom.xml -> version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/avro/pom.xml -> version_backup_20250821_204815./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/kafka-0-10-token-provider/pom.xml -> version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/connect/server/pom.xml -> version_backup_20250821_204815./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/connect/common/pom.xml -> version_backup_20250821_204815./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/connect/client/jvm/pom.xml -> version_backup_20250821_204815./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./connector/protobuf/pom.xml -> version_backup_20250821_204815./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./sql/core/pom.xml -> version_backup_20250821_204815./sql/core/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./sql/catalyst/pom.xml -> version_backup_20250821_204815./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./sql/hive/pom.xml -> version_backup_20250821_204815./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:48:19][0m 备份: ./sql/hive-thriftserver/pom.xml -> version_backup_20250821_204815./sql/hive-thriftserver/pom.xml
-e [0;32m[SUCCESS][0m 备份完成
-e [0;34m[2025-08-21 20:48:19][0m 更新主pom.xml版本...
-e [0;32m[SUCCESS][0m 主pom.xml版本更新成功
-e [0;34m[2025-08-21 20:48:19][0m 更新子模块pom.xml中的父版本引用...
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./resource-managers/yarn/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./resource-managers/mesos/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./resource-managers/kubernetes/core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./launcher/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./launcher/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./tools/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./tools/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./core/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./hadoop-cloud/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./resource-managers/yarn/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./resource-managers/mesos/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./launcher/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./launcher/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./tools/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./tools/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./core/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./hadoop-cloud/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./assembly/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./graphx/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./graphx/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./mllib/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./mllib/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./repl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./repl/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./streaming/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./streaming/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./mllib-local/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./common/network-yarn/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./common/network-common/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./common/network-shuffle/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./common/tags/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./common/tags/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./common/unsafe/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./common/kvstore/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./common/sketch/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./examples/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./examples/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:48:20][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/kafka-0-10/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/kinesis-asl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/docker-integration-tests/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/avro/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/connect/server/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/connect/common/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/connect/client/jvm/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./connector/protobuf/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./sql/core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./sql/core/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./sql/catalyst/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./sql/hive/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_204815./sql/hive-thriftserver/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204815./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./assembly/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./graphx/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./graphx/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./mllib/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./mllib/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./repl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./repl/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./streaming/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./streaming/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./mllib-local/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./common/network-yarn/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./common/network-common/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./common/network-shuffle/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./common/tags/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/tags/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./common/unsafe/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./common/kvstore/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./common/sketch/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./examples/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./examples/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./launcher/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./launcher/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./tools/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./tools/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./core/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./assembly/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./graphx/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./graphx/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./mllib/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./mllib/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./repl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./repl/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./streaming/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./streaming/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./mllib-local/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./common/network-common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./common/tags/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/tags/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./common/unsafe/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./common/kvstore/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./common/sketch/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./examples/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./examples/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/avro/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:48:21][0m 处理: ./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./version_backup_20250821_191406./sql/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./sql/core/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./version_backup_20250821_191406./sql/hive/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/kafka-0-10-assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/kinesis-asl-assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/kafka-0-10-sql/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/kafka-0-10/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/kinesis-asl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/docker-integration-tests/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/spark-ganglia-lgpl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/avro/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/kafka-0-10-token-provider/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/connect/server/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/connect/common/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/connect/client/jvm/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./connector/protobuf/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./sql/core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./sql/core/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./sql/catalyst/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./sql/hive/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:48:22][0m 处理: ./sql/hive-thriftserver/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./sql/hive-thriftserver/pom.xml
-e [0;32m[SUCCESS][0m 子模块父版本更新完成
-e [0;34m[2025-08-21 20:48:22][0m 验证版本更新...
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./launcher/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./tools/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./graphx/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./mllib/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./repl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./streaming/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/tags/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./examples/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./sql/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;32m[SUCCESS][0m 版本验证通过
-e [0;34m[2025-08-21 20:48:23][0m 清理临时文件...
-e [0;32m[SUCCESS][0m 清理完成
-e [0;34m[2025-08-21 20:48:24][0m === 版本更新摘要 ===
-e [0;34m[2025-08-21 20:48:24][0m 原版本: 3.4.4
-e [0;34m[2025-08-21 20:48:24][0m 新版本: 3.4.4_SNAPSHOT
-e [0;34m[2025-08-21 20:48:24][0m 备份目录: version_backup_20250821_204815
-e [0;34m[2025-08-21 20:48:24][0m 日志文件: version_update.log
-e [0;34m[2025-08-21 20:48:24][0m 更新的pom.xml文件数量:      160
-e [0;34m[2025-08-21 20:48:24][0m === 更新完成 ===
-e [0;32m[SUCCESS][0m 版本更新成功完成！
-e [0;34m[2025-08-21 20:49:38][0m === Apache Spark 版本更新开始 ===
-e [0;34m[2025-08-21 20:49:38][0m 当前目录: /Users/<USER>/Documents/IdeaProjects-git/bag_spark_branch-3.4
-e [0;34m[2025-08-21 20:49:38][0m 目标: 3.4.4 -> 3.4.4-20250821-SNAPSHOT
-e [0;34m[2025-08-21 20:49:40][0m 检查必要工具...
-e [0;32m[SUCCESS][0m 工具检查完成
-e [0;34m[2025-08-21 20:49:40][0m 创建备份目录: version_backup_20250821_204938
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./resource-managers/yarn/pom.xml -> version_backup_20250821_204938./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./resource-managers/mesos/pom.xml -> version_backup_20250821_204938./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./resource-managers/kubernetes/core/pom.xml -> version_backup_20250821_204938./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./resource-managers/kubernetes/integration-tests/pom.xml -> version_backup_20250821_204938./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./launcher/pom.xml -> version_backup_20250821_204938./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./tools/pom.xml -> version_backup_20250821_204938./tools/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./core/pom.xml -> version_backup_20250821_204938./core/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./hadoop-cloud/pom.xml -> version_backup_20250821_204938./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./resource-managers/yarn/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./resource-managers/mesos/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./launcher/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./tools/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./tools/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./core/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./core/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./hadoop-cloud/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./assembly/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./graphx/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./mllib/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./repl/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./repl/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./streaming/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./mllib-local/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./common/network-yarn/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./common/network-common/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./common/network-shuffle/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./common/tags/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./common/unsafe/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./common/kvstore/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./common/sketch/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./examples/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./examples/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:41][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/kafka-0-10/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/kinesis-asl/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/docker-integration-tests/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/avro/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/connect/server/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/connect/common/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/connect/client/jvm/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./connector/protobuf/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./sql/core/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./sql/catalyst/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./sql/hive/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_204815./sql/hive-thriftserver/pom.xml -> version_backup_20250821_204938./version_backup_20250821_204815./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./assembly/pom.xml -> version_backup_20250821_204938./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./graphx/pom.xml -> version_backup_20250821_204938./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./mllib/pom.xml -> version_backup_20250821_204938./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./repl/pom.xml -> version_backup_20250821_204938./repl/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./pom.xml -> version_backup_20250821_204938./pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./streaming/pom.xml -> version_backup_20250821_204938./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./mllib-local/pom.xml -> version_backup_20250821_204938./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./common/network-yarn/pom.xml -> version_backup_20250821_204938./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./common/network-common/pom.xml -> version_backup_20250821_204938./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./common/network-shuffle/pom.xml -> version_backup_20250821_204938./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./common/tags/pom.xml -> version_backup_20250821_204938./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./common/unsafe/pom.xml -> version_backup_20250821_204938./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./common/kvstore/pom.xml -> version_backup_20250821_204938./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./common/sketch/pom.xml -> version_backup_20250821_204938./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./examples/pom.xml -> version_backup_20250821_204938./examples/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./resource-managers/yarn/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./resource-managers/mesos/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./launcher/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./tools/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./tools/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./core/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./core/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./hadoop-cloud/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./assembly/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./graphx/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./mllib/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./repl/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./repl/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./streaming/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./mllib-local/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./common/network-yarn/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./common/network-common/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./common/network-shuffle/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./common/tags/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./common/unsafe/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./common/kvstore/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./common/sketch/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./examples/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./examples/pom.xml
-e [0;34m[2025-08-21 20:49:42][0m 备份: ./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/kafka-0-10/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/kinesis-asl/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/avro/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/connect/server/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/connect/common/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./connector/protobuf/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./sql/core/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./sql/catalyst/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./sql/hive/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml -> version_backup_20250821_204938./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/kafka-0-10-assembly/pom.xml -> version_backup_20250821_204938./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/kinesis-asl-assembly/pom.xml -> version_backup_20250821_204938./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/kafka-0-10-sql/pom.xml -> version_backup_20250821_204938./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/kafka-0-10/pom.xml -> version_backup_20250821_204938./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/kinesis-asl/pom.xml -> version_backup_20250821_204938./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/docker-integration-tests/pom.xml -> version_backup_20250821_204938./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/spark-ganglia-lgpl/pom.xml -> version_backup_20250821_204938./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/avro/pom.xml -> version_backup_20250821_204938./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/kafka-0-10-token-provider/pom.xml -> version_backup_20250821_204938./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/connect/server/pom.xml -> version_backup_20250821_204938./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/connect/common/pom.xml -> version_backup_20250821_204938./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/connect/client/jvm/pom.xml -> version_backup_20250821_204938./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./connector/protobuf/pom.xml -> version_backup_20250821_204938./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./sql/core/pom.xml -> version_backup_20250821_204938./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./sql/catalyst/pom.xml -> version_backup_20250821_204938./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./sql/hive/pom.xml -> version_backup_20250821_204938./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:43][0m 备份: ./sql/hive-thriftserver/pom.xml -> version_backup_20250821_204938./sql/hive-thriftserver/pom.xml
-e [0;32m[SUCCESS][0m 备份完成
-e [0;34m[2025-08-21 20:49:43][0m 更新主pom.xml版本...
-e [0;32m[SUCCESS][0m 主pom.xml版本更新成功
-e [0;34m[2025-08-21 20:49:43][0m 更新子模块pom.xml中的父版本引用...
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./resource-managers/yarn/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./resource-managers/mesos/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./resource-managers/kubernetes/core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./launcher/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./tools/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./tools/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./core/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./hadoop-cloud/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./resource-managers/yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./resource-managers/mesos/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./launcher/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./tools/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./tools/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./core/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./hadoop-cloud/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./graphx/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./mllib/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./repl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./repl/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./streaming/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./mllib-local/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./common/network-yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./common/network-common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./common/network-shuffle/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./common/tags/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./common/unsafe/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./common/kvstore/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./common/sketch/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./examples/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./examples/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:44][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/kafka-0-10/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/kinesis-asl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/docker-integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/avro/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/connect/server/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/connect/common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/connect/client/jvm/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./connector/protobuf/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./sql/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./sql/catalyst/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./sql/hive/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./version_backup_20250821_204815./sql/hive-thriftserver/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204815./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./graphx/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./mllib/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:45][0m 处理: ./repl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./repl/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./streaming/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./mllib-local/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./common/network-yarn/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./common/network-common/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./common/network-shuffle/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./common/tags/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./common/unsafe/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./common/kvstore/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./common/sketch/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./examples/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./examples/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./launcher/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./tools/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./tools/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./core/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./graphx/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./mllib/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./repl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./repl/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./streaming/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./mllib-local/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./common/network-common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./common/tags/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./common/unsafe/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./common/kvstore/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./common/sketch/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./examples/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./examples/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:46][0m 处理: ./version_backup_20250821_191406./connector/avro/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_191406./sql/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_191406./sql/hive/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/kafka-0-10-assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/kinesis-asl-assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/kafka-0-10-sql/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/kafka-0-10/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/kinesis-asl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/docker-integration-tests/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/spark-ganglia-lgpl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/avro/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/kafka-0-10-token-provider/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/connect/server/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/connect/common/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/connect/client/jvm/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./connector/protobuf/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./resource-managers/yarn/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./resource-managers/mesos/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./resource-managers/kubernetes/core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./launcher/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./tools/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./tools/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./core/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./hadoop-cloud/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/mesos/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./launcher/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./tools/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./tools/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./core/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./hadoop-cloud/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./graphx/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./mllib/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./repl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./repl/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./streaming/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./mllib-local/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./common/network-yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./common/network-common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./common/network-shuffle/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./common/tags/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./common/unsafe/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:47][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./common/kvstore/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./common/sketch/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./examples/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./examples/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kinesis-asl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/docker-integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/avro/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/server/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:48][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/client/jvm/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/protobuf/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/catalyst/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/hive/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/hive-thriftserver/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./graphx/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./mllib/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./repl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./repl/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./streaming/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./mllib-local/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./common/network-yarn/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./common/network-common/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./common/network-shuffle/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./common/tags/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./common/unsafe/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./common/kvstore/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./common/sketch/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./examples/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./examples/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./launcher/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./launcher/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./tools/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./tools/pom.xml
-e [0;34m[2025-08-21 20:49:49][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./core/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./assembly/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./graphx/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./graphx/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./mllib/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./mllib/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./repl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./repl/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./streaming/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./streaming/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./mllib-local/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./common/network-common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./common/tags/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./common/tags/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./common/unsafe/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./common/kvstore/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./common/sketch/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./examples/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./examples/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/avro/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:50][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/core/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/hive/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [1;33m[WARNING][0m   父版本可能未更新: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/kafka-0-10-assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/kafka-0-10-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/kinesis-asl-assembly/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/kinesis-asl-assembly/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/kafka-0-10-sql/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/kafka-0-10-sql/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/kafka-0-10/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/kafka-0-10/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/kinesis-asl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/kinesis-asl/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/docker-integration-tests/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/docker-integration-tests/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/spark-ganglia-lgpl/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/spark-ganglia-lgpl/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/avro/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/avro/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/kafka-0-10-token-provider/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/kafka-0-10-token-provider/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/connect/server/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/connect/server/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/connect/common/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/connect/common/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/connect/client/jvm/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/connect/client/jvm/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./connector/protobuf/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./connector/protobuf/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./sql/core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./sql/catalyst/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./sql/hive/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./version_backup_20250821_204938./sql/hive-thriftserver/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./version_backup_20250821_204938./sql/hive-thriftserver/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./sql/core/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./sql/core/pom.xml
-e [0;34m[2025-08-21 20:49:51][0m 处理: ./sql/catalyst/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./sql/catalyst/pom.xml
-e [0;34m[2025-08-21 20:49:52][0m 处理: ./sql/hive/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./sql/hive/pom.xml
-e [0;34m[2025-08-21 20:49:52][0m 处理: ./sql/hive-thriftserver/pom.xml
-e [0;32m[SUCCESS][0m   父版本更新成功: ./sql/hive-thriftserver/pom.xml
-e [0;32m[SUCCESS][0m 子模块父版本更新完成
-e [0;34m[2025-08-21 20:49:52][0m 验证版本更新...
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./resource-managers/yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./resource-managers/mesos/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./launcher/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./tools/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./hadoop-cloud/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./graphx/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./mllib/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./repl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./streaming/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./mllib-local/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./common/network-yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./common/network-common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./common/network-shuffle/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./common/tags/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./common/unsafe/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./common/kvstore/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./common/sketch/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./examples/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/kafka-0-10/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/kinesis-asl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/docker-integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/avro/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/connect/server/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/connect/common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/connect/client/jvm/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./connector/protobuf/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./sql/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./sql/catalyst/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./sql/hive/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204815./sql/hive-thriftserver/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./launcher/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./tools/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./graphx/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./mllib/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./repl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./streaming/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/tags/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./examples/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./sql/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/mesos/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/kubernetes/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./launcher/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./tools/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./hadoop-cloud/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./graphx/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./mllib/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./repl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./streaming/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./mllib-local/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./common/network-yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./common/network-common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./common/network-shuffle/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./common/tags/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./common/unsafe/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./common/kvstore/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./common/sketch/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./examples/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./launcher/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./tools/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./graphx/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./mllib/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./repl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./streaming/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/tags/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./examples/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kinesis-asl-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-sql/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kinesis-asl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/docker-integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/spark-ganglia-lgpl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/avro/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/kafka-0-10-token-provider/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/server/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/connect/client/jvm/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./connector/protobuf/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/catalyst/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/hive/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_204815./sql/hive-thriftserver/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/mesos/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/kubernetes/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./resource-managers/kubernetes/integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./launcher/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./tools/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./hadoop-cloud/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./graphx/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./mllib/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./repl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./streaming/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./mllib-local/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./common/network-yarn/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./common/network-common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./common/network-shuffle/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./common/tags/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./common/unsafe/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./common/kvstore/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./common/sketch/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./examples/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kinesis-asl-assembly/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-sql/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kinesis-asl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/docker-integration-tests/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/spark-ganglia-lgpl/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/avro/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/kafka-0-10-token-provider/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/server/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/common/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/connect/client/jvm/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./connector/protobuf/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/core/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/catalyst/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/hive/pom.xml
-e [0;31m[ERROR][0m 子模块父版本验证失败: ./version_backup_20250821_204938./version_backup_20250821_191406./sql/hive-thriftserver/pom.xml
-e [0;32m[SUCCESS][0m 版本验证通过
-e [0;34m[2025-08-21 20:49:57][0m 清理临时文件...
-e [0;32m[SUCCESS][0m 清理完成
-e [0;34m[2025-08-21 20:49:58][0m === 版本更新摘要 ===
-e [0;34m[2025-08-21 20:49:58][0m 原版本: 3.4.4
-e [0;34m[2025-08-21 20:49:58][0m 新版本: 3.4.4-20250821-SNAPSHOT
-e [0;34m[2025-08-21 20:49:58][0m 备份目录: version_backup_20250821_204938
-e [0;34m[2025-08-21 20:49:58][0m 日志文件: version_update.log
-e [0;34m[2025-08-21 20:49:59][0m 更新的pom.xml文件数量:      320
-e [0;34m[2025-08-21 20:49:59][0m === 更新完成 ===
-e [0;32m[SUCCESS][0m 版本更新成功完成！
