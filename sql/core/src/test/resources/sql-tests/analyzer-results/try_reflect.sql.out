-- Automatically generated by SQLQueryTestSuite
-- !query
SELECT try_reflect("java.util.UUID", "fromString", "a5cf6c42-0c85-418f-af6c-3e4e5b1328f2")
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
SELECT try_reflect("java.lang.String", "valueOf", 1)
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
SELECT try_reflect("java.lang.Math", "max", 2, 3)
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
SELECT try_reflect("java.lang.Math", "min", 2, 3)
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
SELECT try_reflect("java.lang.Integer", "valueOf", "10", 16)
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
SELECT try_reflect("java.util.UUID", "fromString", "b")
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
SELECT try_reflect("java.net.URLDecoder", "decode", "%")
-- !query analysis
[Analyzer test output redacted due to nondeterminism]


-- !query
SELECT try_reflect("java.wrongclass.Math", "max", 2, 3)
-- !query analysis
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_CLASS_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "className" : "java.wrongclass.Math",
    "sqlExpr" : "\"reflect(java.wrongclass.Math, max, 2, 3)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 55,
    "fragment" : "try_reflect(\"java.wrongclass.Math\", \"max\", 2, 3)"
  } ]
}


-- !query
SELECT try_reflect("java.lang.Math", "wrongmethod", 2, 3)
-- !query analysis
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_STATIC_METHOD",
  "sqlState" : "42K09",
  "messageParameters" : {
    "className" : "java.lang.Math",
    "methodName" : "wrongmethod",
    "sqlExpr" : "\"reflect(java.lang.Math, wrongmethod, 2, 3)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 57,
    "fragment" : "try_reflect(\"java.lang.Math\", \"wrongmethod\", 2, 3)"
  } ]
}


-- !query
SELECT try_reflect("java.lang.Math")
-- !query analysis
org.apache.spark.sql.AnalysisException
{
  "errorClass" : "WRONG_NUM_ARGS.WITHOUT_SUGGESTION",
  "sqlState" : "42605",
  "messageParameters" : {
    "actualNum" : "1",
    "docroot" : "https://spark.apache.org/docs/latest",
    "expectedNum" : "> 1",
    "functionName" : "`reflect`"
  }
}


-- !query
SELECT try_reflect("java.lang.Math", "round", 2.5)
-- !query analysis
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"2.5\"",
    "inputType" : "\"DECIMAL(2,1)\"",
    "paramIndex" : "3",
    "requiredType" : "(\"BOOLEAN\" or \"TINYINT\" or \"SMALLINT\" or \"INT\" or \"BIGINT\" or \"FLOAT\" or \"DOUBLE\" or \"STRING\")",
    "sqlExpr" : "\"reflect(java.lang.Math, round, 2.5)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 50,
    "fragment" : "try_reflect(\"java.lang.Math\", \"round\", 2.5)"
  } ]
}


-- !query
SELECT try_reflect("java.lang.Object", "toString")
-- !query analysis
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_STATIC_METHOD",
  "sqlState" : "42K09",
  "messageParameters" : {
    "className" : "java.lang.Object",
    "methodName" : "toString",
    "sqlExpr" : "\"reflect(java.lang.Object, toString)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 50,
    "fragment" : "try_reflect(\"java.lang.Object\", \"toString\")"
  } ]
}
