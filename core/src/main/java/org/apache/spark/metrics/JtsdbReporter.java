/*
 * Copyright 2014 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.spark.metrics;

import java.util.Collection;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import com.codahale.metrics.Clock;
import com.codahale.metrics.Counter;
import com.codahale.metrics.Gauge;
import com.codahale.metrics.Histogram;
import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricFilter;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.ScheduledReporter;
import com.codahale.metrics.Snapshot;
import com.codahale.metrics.Timer;

import com.google.common.collect.ImmutableMap;
import org.apache.spark.metrics.hdfs.HdfsClientMetricSet;
import org.apache.spark.metrics.jtsdb.Jtsdb;
import org.apache.spark.metrics.jtsdb.JtsdbMetric;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/**
 * A reporter which publishes metric values to a JTSDB server.
 *
 */
public class JtsdbReporter extends ScheduledReporter {

  private final Jtsdb jtsdb;
  private final Clock clock;
  private final String prefix;
  private final String prefixToRemove;
  private final ImmutableMap<String, String> tags;
  private ScheduledExecutorService scheduledExecutorService;
  private static final Logger logger = LoggerFactory.getLogger(JtsdbReporter.class);
  /**
   * Returns a new {@link Builder} for {@link JtsdbReporter}.
   *
   * @param registry the registry to report
   * @return a {@link Builder} instance for a {@link JtsdbReporter}
   */
  public static Builder forRegistry(MetricRegistry registry) {
    return new Builder(registry);
  }

  /**
   * A builder for {@link JtsdbReporter} instances. Defaults to not using a prefix, using the
   * default clock, converting rates to events/second, converting durations to milliseconds, and
   * not filtering metrics.
   */
  public static class Builder {
    private final MetricRegistry registry;
    private Clock clock;
    private String prefix;
    private String prefixToRemove;
    private TimeUnit rateUnit;
    private TimeUnit durationUnit;
    private MetricFilter filter;
    private Map<String, String> tags;
    private ScheduledExecutorService scheduledExecutorService;

    private Builder(MetricRegistry registry) {
      this.registry = registry;
      this.clock = Clock.defaultClock();
      this.prefix = null;
      this.rateUnit = TimeUnit.SECONDS;
      this.durationUnit = TimeUnit.MILLISECONDS;
      this.filter = MetricFilter.ALL;
      this.scheduledExecutorService = null;
    }

    /**
     * Use the given {@link Clock} instance for the time.
     *
     * @param clock a {@link Clock} instance
     * @return {@code this}
     */
    public Builder withClock(Clock clock) {
      this.clock = clock;
      return this;
    }

    /**
     * Prefix all metric names with the given string.
     *
     * @param prefix the prefix for all metric names
     * @return {@code this}
     */
    public Builder prefixedWith(String prefix) {
      this.prefix = prefix;
      return this;
    }

    public Builder removePrefix(String prefixToRemove) {
      this.prefixToRemove = prefixToRemove;
      return this;
    }

    /**
     * Convert rates to the given time unit.
     *
     * @param rateUnit a unit of time
     * @return {@code this}
     */
    public Builder convertRatesTo(TimeUnit rateUnit) {
      this.rateUnit = rateUnit;
      return this;
    }

    /**
     * Convert durations to the given time unit.
     *
     * @param durationUnit a unit of time
     * @return {@code this}
     */
    public Builder convertDurationsTo(TimeUnit durationUnit) {
      this.durationUnit = durationUnit;
      return this;
    }

    /**
     * Only report metrics which match the given filter.
     *
     * @param filter a {@link MetricFilter}
     * @return {@code this}
     */
    public Builder filter(MetricFilter filter) {
      this.filter = filter;
      return this;
    }

    /**
     * Append tags to all reported metrics
     *
     * @param tags
     * @return
     */
    public Builder withTags(Map<String, String> tags) {
      this.tags = tags;
      return this;
    }

    public Builder withScheduledExecutorService(ScheduledExecutorService scheduledExecutorService) {
      this.scheduledExecutorService = scheduledExecutorService;
      return this;
    }

    /**
     * Builds a {@link JtsdbReporter} with the given properties, sending metrics using the
     * given {@link Jtsdb} client.
     *
     * @param jtsdb a {@link Jtsdb} client
     * @return a {@link JtsdbReporter}
     */
    public JtsdbReporter build(Jtsdb jtsdb) {
      if (scheduledExecutorService == null) {
        return new JtsdbReporter(
          registry, jtsdb, clock, prefix, prefixToRemove, rateUnit, durationUnit, filter, tags);
      } else {
        return new JtsdbReporter(scheduledExecutorService,
          registry, jtsdb, clock, prefix, prefixToRemove, rateUnit, durationUnit, filter, tags);
      }
    }
  }

  private static class MetricsCollector {
    private final String prefix;
    private final ImmutableMap<String, String> tags;
    private final long timestamp;
    private final Set<JtsdbMetric> metrics = new HashSet<JtsdbMetric>();

    private MetricsCollector(String prefix, ImmutableMap<String, String> tags, long timestamp) {
      this.prefix = prefix;
      this.tags = tags;
      this.timestamp = timestamp;
    }

    public static MetricsCollector createNew(
      String prefix, ImmutableMap<String, String> tags, long timestamp) {
      return new MetricsCollector(prefix, tags, timestamp);
    }

    public MetricsCollector addMetric(String metricName, Object value) {
      this.metrics.add(JtsdbMetric.named(MetricRegistry.name(prefix, metricName))
        .withTimestamp(timestamp)
        .withValue(value)
        .withTags(tags).build());
      return this;
    }

    public Set<JtsdbMetric> build() {
      return metrics;
    }
  }

  private JtsdbReporter(
      ScheduledExecutorService scheduledExecutorService,
      MetricRegistry registry, Jtsdb jtsdb, Clock clock,
      String prefix, String prefixToRemove,
      TimeUnit rateUnit, TimeUnit durationUnit, MetricFilter filter, Map<String, String> tags) {
    super(registry, "jtsdb-reporter", filter, rateUnit, durationUnit, scheduledExecutorService);
    this.scheduledExecutorService = scheduledExecutorService;
    this.jtsdb = jtsdb;
    this.clock = clock;
    this.prefix = prefix;
    this.prefixToRemove = prefixToRemove;
    this.tags = ImmutableMap.copyOf(tags);
  }

  private JtsdbReporter(
    MetricRegistry registry, Jtsdb jtsdb, Clock clock,
    String prefix, String prefixToRemove,
    TimeUnit rateUnit, TimeUnit durationUnit, MetricFilter filter, Map<String, String> tags) {
    super(registry, "jtsdb-reporter", filter, rateUnit, durationUnit);
    this.jtsdb = jtsdb;
    this.clock = clock;
    this.prefix = prefix;
    this.prefixToRemove = prefixToRemove;
    this.tags = ImmutableMap.copyOf(tags);
  }

  @Override
  public void report(
    SortedMap<String, Gauge> gauges, SortedMap<String, Counter> counters,
    SortedMap<String, Histogram> histograms, SortedMap<String, Meter> meters,
    SortedMap<String, Timer> timers) {

    final long timestamp = clock.getTime() / 1000;

    final Set<JtsdbMetric> metrics = new HashSet<JtsdbMetric>();

    for (Map.Entry<String, Gauge> g : gauges.entrySet()) {
      Object value = g.getValue().getValue();
      if(value instanceof Collection && ((Collection) value).isEmpty()) {
        continue;
      }
      metrics.add(buildGauge(g.getKey(), value, timestamp));
    }

    for (Map.Entry<String, Counter> entry : counters.entrySet()) {
      metrics.add(buildCounter(entry.getKey(), entry.getValue(), timestamp));
    }

    for (Map.Entry<String, Histogram> entry : histograms.entrySet()) {
      metrics.addAll(buildHistograms(entry.getKey(), entry.getValue(), timestamp));
    }

    for (Map.Entry<String, Meter> entry : meters.entrySet()) {
      metrics.addAll(buildMeters(entry.getKey(), entry.getValue(), timestamp));
    }

    for (Map.Entry<String, Timer> entry : timers.entrySet()) {
      metrics.addAll(buildTimers(entry.getKey(), entry.getValue(), timestamp));
    }

    Map<String, Gauge> hdfsClientGauges = HdfsClientMetricSet.getInstance().getMetrics();
    for (Map.Entry<String, Gauge> g : hdfsClientGauges.entrySet()) {
      Object value = g.getValue().getValue();
      if(value instanceof Collection && ((Collection) value).isEmpty()) {
        continue;
      }
      metrics.add(buildGauge(g.getKey(), value, timestamp));
    }

    jtsdb.send(metrics);
  }


  @Override
  public void stop() {
    jtsdb.stop();
    super.stop();
  }

  @Override
  public void start(long period, TimeUnit unit) {
    long initialDelay = period + (long) (Math.random() * period);
    scheduledExecutorService.scheduleAtFixedRate(new Runnable() {
      @Override
      public void run() {
        try {
          report();
        } catch (Exception e) {
          logger.warn("Exception thrown from #report. Exception was suppressed.");
        }
      }
    }, initialDelay, period, unit);
  }

  private Set<JtsdbMetric> buildTimers(String name, Timer timer, long timestamp) {
    final MetricsCollector collector = MetricsCollector.createNew(
      prefix(name.replace("$", "-")), tags, timestamp);
    final Snapshot snapshot = timer.getSnapshot();

    return collector
      .addMetric("count", timer.getCount())
      //convert rate
      .addMetric("m5", convertRate(timer.getFiveMinuteRate()))
      .addMetric("m1", convertRate(timer.getOneMinuteRate()))
      // convert duration
      .addMetric("max", convertDuration(snapshot.getMax()))
      .addMetric("min", convertDuration(snapshot.getMin()))
      .addMetric("stddev", convertDuration(snapshot.getStdDev()))
      .addMetric("median", convertDuration(snapshot.getMedian()))
      .addMetric("p90", convertDuration(snapshot.getValue(0.90D)))
      .addMetric("p99", convertDuration(snapshot.get99thPercentile()))
      .build();
  }

  private Set<JtsdbMetric> buildHistograms(String name, Histogram histogram, long timestamp) {

    final MetricsCollector collector = MetricsCollector.createNew(prefix(name), tags, timestamp);
    final Snapshot snapshot = histogram.getSnapshot();

    return collector
      .addMetric("count", histogram.getCount())
      .addMetric("max", snapshot.getMax())
      .addMetric("min", snapshot.getMin())
      .addMetric("stddev", snapshot.getStdDev())
      .addMetric("median", snapshot.getMedian())
      .addMetric("p90", snapshot.getValue(0.90D))
      .addMetric("p99", snapshot.get99thPercentile())
      .build();
  }

  private Set<JtsdbMetric> buildMeters(String name, Meter meter, long timestamp) {

    final MetricsCollector collector = MetricsCollector.createNew(prefix(name), tags, timestamp);

    return collector
      .addMetric("count", meter.getCount())
      // convert rate
      .addMetric("m1", convertRate(meter.getOneMinuteRate()))
      .addMetric("m5", convertRate(meter.getFiveMinuteRate()))
      .build();
  }

  private JtsdbMetric buildCounter(String name, Counter counter, long timestamp) {
    return JtsdbMetric.named(prefix(name, "count"))
      .withTimestamp(timestamp)
      .withValue(counter.getCount())
      .withTags(tags)
      .build();
  }

  private JtsdbMetric buildGauge(String name, Object gauge, long timestamp) {
    return JtsdbMetric.named(prefix(name, "value"))
      .withValue(gauge)
      .withTimestamp(timestamp)
      .withTags(tags)
      .build();
  }

  private String prefix(String... components) {
    if (prefixToRemove != null) {
      components[0] = components[0].replace(prefixToRemove, "");
    }
   return MetricRegistry.name(prefix, components);
  }
}
