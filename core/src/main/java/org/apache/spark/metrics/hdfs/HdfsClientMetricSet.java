/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.metrics.hdfs;

import com.codahale.metrics.Gauge;
import com.codahale.metrics.MetricRegistry;

import java.lang.management.ManagementFactory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Set;

import org.apache.hadoop.metrics2.lib.DefaultMetricsSystem;
import org.apache.spark.SparkEnv;
import org.apache.spark.metrics.MetricsSystem;
import org.apache.spark.metrics.source.HdfsClientSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.spark.util.BDPUtils;

import javax.management.MBeanInfo;
import javax.management.MBeanAttributeInfo;
import javax.management.MBeanServerConnection;
import javax.management.ObjectName;


public class HdfsClientMetricSet {
    private static final Logger logger = LoggerFactory.getLogger(HdfsClientMetricSet.class);

    private static final String service = "dfsclient";
    private static final String dfsClientObjectName = "Hadoop:service=*,name=DFSClientDetail-*";
    private static final String tagContext = "tag.Context";
    private static final String tagHostname = "tag.Hostname";
    private static final String tagNs = "tag.Ns";
    private static String regName = "";

    private volatile static HdfsClientMetricSet metricSet;

    public HdfsClientMetricSet() {
        SparkEnv sparkEnv = SparkEnv.get();
        if (sparkEnv != null) {
            MetricsSystem metricsSystem = SparkEnv.get().metricsSystem();
            regName = metricsSystem.buildRegistryName(new HdfsClientSource());
        }
    }

    public static HdfsClientMetricSet getInstance() {
        if (metricSet == null) {
            synchronized (HdfsClientMetricSet.class) {
                if (metricSet == null) {
                    DefaultMetricsSystem.initialize(service);
                    metricSet = new HdfsClientMetricSet();
                }
            }
        }

        return metricSet;
    }

    public Map<String, Gauge> getMetrics() {
        final Map<String, Gauge> gauges = new HashMap<String, Gauge>();

        MBeanServerConnection mbsc = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName objectName = new ObjectName(dfsClientObjectName);
            Set<ObjectName> beanSet = mbsc.queryNames(objectName, null);
            for (ObjectName bean : beanSet) {
                Map<String, String> mapTags = new HashMap<>();
                mapTags.put("bz_flag", "true");
                Object context = mbsc.getAttribute(bean, tagContext);
                if (context != null) {
                    mapTags.put("Context", context.toString());
                }
                Object hostname = mbsc.getAttribute(bean, tagHostname);
                if (hostname != null) {
                    mapTags.put("Hostname", hostname.toString());
                }
                Object nsid = mbsc.getAttribute(bean, tagNs);
                if (nsid != null) {
                    mapTags.put("nsid", nsid.toString());
                }
                String strTags = tagsToString(mapTags);

                MBeanInfo info = mbsc.getMBeanInfo(bean);
                MBeanAttributeInfo[] attributes = info.getAttributes();
                for (MBeanAttributeInfo attribute : attributes) {
                    if (tagContext.equals(attribute.getName()) ||
                            tagHostname.equals(attribute.getName()) ||
                            tagNs.equals(attribute.getName())) {
                        continue;
                    }
                    String metricName = MetricRegistry.name(regName, strTags, attribute.getName());
                    Object metricValue = mbsc.getAttribute(bean, attribute.getName());
                    if (metricValue == null) {
                        logger.warn("getMetrics getAttribute value is null, bean:" + bean + ", name:" + metricName);
                        continue;
                    }

                    String valType = attribute.getType();
                    switch (valType) {
                        case "java.lang.Long":
                            gauges.put(metricName, new Gauge<Long>() {
                                @Override
                                public Long getValue() {
                                    return Long.valueOf(metricValue.toString());
                                }
                            });
                            break;
                        case "java.lang.Integer":
                            gauges.put(metricName, new Gauge<Integer>() {
                                @Override
                                public Integer getValue() {
                                    return Integer.valueOf(metricValue.toString());
                                }
                            });
                            break;
                        case "java.lang.Float":
                            gauges.put(metricName, new Gauge<Float>() {
                                @Override
                                public Float getValue() {
                                    return Float.valueOf(metricValue.toString());
                                }
                            });
                            break;
                        case "java.lang.Double":
                            gauges.put(metricName, new Gauge<Double>() {
                                @Override
                                public Double getValue() {
                                    return Double.valueOf(metricValue.toString());
                                }
                            });
                            break;
                        default:
                            logger.warn("getMetrics value is not number, bean:" + bean + ", metricName:" + metricName + ", valType:" + valType);
                            break;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("getMetrics exception:", e);
        }

        return Collections.unmodifiableMap(gauges);
    }

    public String tagsToString(Map<String, String> tags) {
        if (tags == null || tags.isEmpty()) {
            return "";
        }

        List<String> arr = new ArrayList<>();
        tags.forEach((key, value) ->{
            arr.add(BDPUtils.tagToString(key, value));
        });
        return String.join(".", arr);
    }
}
