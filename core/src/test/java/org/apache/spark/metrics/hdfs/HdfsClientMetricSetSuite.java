/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.spark.metrics.hdfs;

import com.codahale.metrics.Gauge;
import com.codahale.metrics.Metric;
import org.apache.spark.SparkConf;
import org.apache.spark.SparkContext;
import org.junit.Before;
import org.junit.Test;

import javax.management.Attribute;
import javax.management.MBeanServer;
import javax.management.ObjectName;
import javax.management.modelmbean.*;
import java.lang.management.ManagementFactory;
import java.util.Map;

import static org.junit.Assert.assertEquals;

public class HdfsClientMetricSetSuite {
    @Before
    public void setup(){
        // write data to MBean
        writeMBean();

        // Create a SparkContext as a convenient way of setting SparkEnv (needed because some of the
        // HdfsClientMetricSet code calls SparkEnv.get()).
        SparkConf testConf = new SparkConf(false);
        new SparkContext("local", "test", testConf);
    }

    @Test
    public void testGetMetrics() {
        HdfsClientMetricSet metricSet = HdfsClientMetricSet.getInstance();
        Map<String, Gauge> metricMap = metricSet.getMetrics();
        metricMap.forEach((key, value) -> {
            System.out.println(key + ": " + value.getValue());
        });
        assertEquals(4, metricMap.size());
    }

    private void writeMBean() {
        String contextName = "tag.Context";
        String hostnameName = "tag.Hostname";
        String nsName = "tag.Ns";
        String readOpsName = "ReadOps";
        String readLengthName = "ReadLength";
        String readRateName = "ReadRate";
        String readCostName = "ReadCost";
        String curTimeName = "CurTime";

        try {
            // get MBean
            ModelMBeanAttributeInfo[] attributes = new ModelMBeanAttributeInfo[] {
                    new ModelMBeanAttributeInfo(contextName, "java.lang.String", "test info", true, true, false),
                    new ModelMBeanAttributeInfo(hostnameName, "java.lang.String", "test info", true, true, false),
                    new ModelMBeanAttributeInfo(nsName, "java.lang.String", "test info", true, true, false),
                    new ModelMBeanAttributeInfo(readOpsName, "java.lang.Integer", "test info", true, true, false),
                    new ModelMBeanAttributeInfo(readLengthName, "java.lang.Long", "test info", true, true, false),
                    new ModelMBeanAttributeInfo(readRateName, "java.lang.Float", "test info", true, true, false),
                    new ModelMBeanAttributeInfo(readCostName, "java.lang.Double", "test info", true, true, false),
                    new ModelMBeanAttributeInfo(curTimeName, "java.lang.String", "test info", true, true, false)
            };
            ModelMBean mbean = new RequiredModelMBean();
            mbean.setModelMBeanInfo(new ModelMBeanInfoSupport(
                    "Hadoop",
                    "Hadoop ModelMBean",
                    attributes,
                    null,
                    null,
                    null
            ));
            mbean.setManagedResource(new DfsClientResource(), "ObjectReference");
            ObjectName objectName = new ObjectName("Hadoop:service=testclient,name=DFSClientDetail-ns1");

            // register MBean
            MBeanServer mbs = ManagementFactory.getPlatformMBeanServer();
            mbs.registerMBean(mbean, objectName);

            // set attributes
            mbs.setAttribute(objectName, new Attribute(contextName, "dfsclient"));
            mbs.setAttribute(objectName, new Attribute(hostnameName, "jd-test.jd.local"));
            mbs.setAttribute(objectName, new Attribute(nsName, "ns1"));
            mbs.setAttribute(objectName, new Attribute(readLengthName, 100L));
            mbs.setAttribute(objectName, new Attribute(readOpsName, 200));
            mbs.setAttribute(objectName, new Attribute(readRateName, 300.50f));
            mbs.setAttribute(objectName, new Attribute(readCostName, 400.50));
            mbs.setAttribute(objectName, new Attribute(curTimeName, "2025-06-20"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public class DfsClientResource {
        private String context;
        private String hostname;
        private String ns;
        private Long readLength;
        private Integer readOps;
        private Float readRate;
        private Double readCost;
        private String curTime;

        public String getContext() {
            return context;
        }

        public void setContext(String value) {
            this.context = value;
        }

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String value) {
            this.hostname = value;
        }

        public String getNs() {
            return ns;
        }

        public void setNs(String value) {
            this.ns = value;
        }

        public Long getReadLength() {
            return readLength;
        }

        public void setReadLength(Long value) {
            this.readLength = value;
        }

        public Integer getReadOps() {
            return readOps;
        }

        public void setReadOps(Integer value) {
            this.readOps = value;
        }

        public Float getReadRate() {
            return readRate;
        }

        public void setReadRate(Float value) {
            this.readRate = value;
        }

        public Double getReadCost() {
            return readCost;
        }

        public void setReadCost(Double value) {
            this.readCost = value;
        }

        public String getCurTime() {
            return curTime;
        }

        public void setCurTime(String value) {
            this.curTime = value;
        }
    }
}